﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - DKyThucTap</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/DKyThucTap.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-briefcase me-2"></i>DKyThucTap
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>Trang chủ
                            </a>
                        </li>

                        <!-- Positions Menu -->
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Position" asp-action="Index">
                                <i class="fas fa-briefcase me-1"></i>Việc làm
                            </a>
                        </li>

                        <!-- Companies Menu -->
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Company" asp-action="Index">
                                <i class="fas fa-building me-1"></i>Công ty
                            </a>
                        </li>

                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            @if (User.HasClaim("Permission", "create_position") || User.HasClaim("Permission", "create_company"))
                            {
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-plus-circle me-1"></i>Quản lý
                                    </a>
                                    <ul class="dropdown-menu">
                                        @if (User.HasClaim("Permission", "create_position"))
                                        {
                                            <li><a class="dropdown-item" asp-controller="Position" asp-action="Create">
                                                <i class="fas fa-plus me-1"></i>Đăng tin tuyển dụng</a></li>
                                            <li><a class="dropdown-item" asp-controller="Position" asp-action="My">
                                                <i class="fas fa-list me-1"></i>Vị trí của tôi</a></li>
                                        }
                                        @if (User.HasClaim("Permission", "create_company"))
                                        {
                                            @if (User.HasClaim("Permission", "create_position"))
                                            {
                                                <li><hr class="dropdown-divider"></li>
                                            }
                                            <li><a class="dropdown-item" asp-controller="Company" asp-action="Create">
                                                <i class="fas fa-plus me-1"></i>Tạo công ty</a></li>
                                            <li><a class="dropdown-item" asp-controller="Company" asp-action="My">
                                                <i class="fas fa-building me-1"></i>Công ty của tôi</a></li>
                                            <li><a class="dropdown-item" asp-controller="Company" asp-action="MyRequests">
                                                <i class="fas fa-hourglass-half me-1"></i>Yêu cầu của tôi</a></li>
                                        }
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-list me-1"></i>Quản lý tin đăng</a></li>
                                    </ul>
                                </li>
                            }

                            @if (User.HasClaim("Permission", "manage_applications"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link" href="#">
                                        <i class="fas fa-users me-1"></i>Ứng viên
                                    </a>
                                </li>
                            }

                            @if (User.HasClaim("Permission", "create_company"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link" href="#">
                                        <i class="fas fa-building me-1"></i>Công ty
                                    </a>
                                </li>
                            }

                            @if (User.HasClaim("Permission", "manage_users"))
                            {
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-cog me-1"></i>Quản trị
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-users me-1"></i>Quản lý người dùng</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-building me-1"></i>Quản lý công ty</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-star me-1"></i>Duyệt đánh giá</a></li>
                                    </ul>
                                </li>
                            }
                        }
                    </ul>

                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle me-1"></i>
                                    @(User.FindFirst("FullName")?.Value ?? User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value)
                                    <span class="badge bg-light text-primary ms-1">@User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" asp-controller="Account" asp-action="Dashboard">
                                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" asp-controller="Account" asp-action="Profile">
                                            <i class="fas fa-user-edit me-1"></i>Chỉnh sửa thông tin
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" asp-controller="Account" asp-action="Settings">
                                            <i class="fas fa-cog me-1"></i>Cài đặt
                                        </a>
                                    </li>
                                    @if (User.HasClaim("Permission", "send_messages"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" href="#">
                                                <i class="fas fa-envelope me-1"></i>Tin nhắn
                                                <span class="badge bg-danger ms-1">3</span>
                                            </a>
                                        </li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Auth" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="fas fa-sign-out-alt me-1"></i>Đăng xuất
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Auth" asp-action="Login">
                                    <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Auth" asp-action="Register">
                                    <i class="fas fa-user-plus me-1"></i>Đăng ký
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - DKyThucTap - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)

@* <script>
// Check and display notifications from other pages
document.addEventListener('DOMContentLoaded', function() {
    const notification = sessionStorage.getItem('positionNotification');
    if (notification) {
        const { type, message, timestamp } = JSON.parse(notification);

        const now = new Date().getTime();
        if (now - timestamp < 5000) {
            if (typeof showNotification === 'function') {
                showNotification(type, message);
            } else {
                // Fallback if showNotification isn't defined on the page
                const container = document.createElement('div');
                container.id = 'notificationContainer';
                container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                document.body.appendChild(container);

                const alert = document.createElement('div');
                alert.className = `alert alert-${type} alert-dismissible fade show`;
                alert.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                container.appendChild(alert);

                setTimeout(() => {
                    alert.remove();
                }, 5000);
            }
        }
        // Clear the notification after showing it
        sessionStorage.removeItem('positionNotification');
    }
});
</script>

<style>
.alert {
    min-width: 300px;
    margin-bottom: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
}

@@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style> *@
</body>
</html>

USE DKyThucTap;
GO

-- Enhanced Company Recruiters Schema Update Script
-- This script adds missing columns and features for the advanced recruiter management system

PRINT '🚀 Starting enhanced company_recruiters schema update...';

-- Check if company_recruiters table exists, if not create it
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'company_recruiters')
BEGIN
    PRINT '1. Creating the company_recruiters table...';
    CREATE TABLE company_recruiters (
        user_id INT NOT NULL,
        company_id INT NOT NULL,
        role_in_company NVARCHAR(100) NULL,
        is_admin BIT DEFAULT 0,
        assigned_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
        PRIMARY KEY (user_id, company_id),
        CONSTRAINT FK_company_recruiters_users FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        CONSTRAINT FK_company_recruiters_companies FOREIGN KEY (company_id) REFERENCES companies(company_id) ON DELETE CASCADE
    );
    
    -- Populate with existing company creators
    INSERT INTO company_recruiters (user_id, company_id, role_in_company, is_admin)
    SELECT
        created_by,
        company_id,
        N'Chủ sở hữu',
        1
    FROM companies
    WHERE created_by IS NOT NULL;
END
ELSE
BEGIN
    PRINT '1. company_recruiters table already exists, proceeding with column updates...';
END
GO

-- Add missing columns for enhanced functionality
PRINT '2. Adding enhanced columns to company_recruiters table...';

-- Add is_approved column for invitation/request approval system
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'is_approved')
BEGIN
    ALTER TABLE company_recruiters ADD is_approved BIT DEFAULT 1;
    PRINT '   ✅ Added is_approved column';
END
ELSE
BEGIN
    PRINT '   ℹ️  is_approved column already exists';
END

-- Add joined_at column (separate from assigned_at for better tracking)
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'joined_at')
BEGIN
    ALTER TABLE company_recruiters ADD joined_at DATETIMEOFFSET DEFAULT GETUTCDATE();
    PRINT '   ✅ Added joined_at column';
END
ELSE
BEGIN
    PRINT '   ℹ️  joined_at column already exists';
END

-- Add request_message column for join requests
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'request_message')
BEGIN
    ALTER TABLE company_recruiters ADD request_message NVARCHAR(500) NULL;
    PRINT '   ✅ Added request_message column';
END
ELSE
BEGIN
    PRINT '   ℹ️  request_message column already exists';
END

-- Add response_message column for approval/rejection responses
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'response_message')
BEGIN
    ALTER TABLE company_recruiters ADD response_message NVARCHAR(500) NULL;
    PRINT '   ✅ Added response_message column';
END
ELSE
BEGIN
    PRINT '   ℹ️  response_message column already exists';
END

-- Add invited_by column to track who invited the recruiter
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'invited_by')
BEGIN
    ALTER TABLE company_recruiters ADD invited_by INT NULL;
    PRINT '   ✅ Added invited_by column';
    
    -- Add foreign key constraint
    ALTER TABLE company_recruiters 
    ADD CONSTRAINT FK_company_recruiters_invited_by 
    FOREIGN KEY (invited_by) REFERENCES users(user_id);
    PRINT '   ✅ Added FK constraint for invited_by';
END
ELSE
BEGIN
    PRINT '   ℹ️  invited_by column already exists';
END

-- Add responded_by column to track who approved/rejected the request
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'responded_by')
BEGIN
    ALTER TABLE company_recruiters ADD responded_by INT NULL;
    PRINT '   ✅ Added responded_by column';
    
    -- Add foreign key constraint
    ALTER TABLE company_recruiters 
    ADD CONSTRAINT FK_company_recruiters_responded_by 
    FOREIGN KEY (responded_by) REFERENCES users(user_id);
    PRINT '   ✅ Added FK constraint for responded_by';
END
ELSE
BEGIN
    PRINT '   ℹ️  responded_by column already exists';
END

-- Add responded_at column to track when the response was made
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'responded_at')
BEGIN
    ALTER TABLE company_recruiters ADD responded_at DATETIMEOFFSET NULL;
    PRINT '   ✅ Added responded_at column';
END
ELSE
BEGIN
    PRINT '   ℹ️  responded_at column already exists';
END

-- Add status column for better tracking (pending, approved, rejected, left)
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'status')
BEGIN
    ALTER TABLE company_recruiters ADD status NVARCHAR(20) DEFAULT 'approved';
    PRINT '   ✅ Added status column';
END
ELSE
BEGIN
    PRINT '   ℹ️  status column already exists';
END

-- Add last_activity column for performance tracking
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('company_recruiters') AND name = 'last_activity')
BEGIN
    ALTER TABLE company_recruiters ADD last_activity DATETIMEOFFSET DEFAULT GETUTCDATE();
    PRINT '   ✅ Added last_activity column';
END
ELSE
BEGIN
    PRINT '   ℹ️  last_activity column already exists';
END

GO

-- Update existing records to have proper default values
PRINT '3. Updating existing records with default values...';

-- Set is_approved = 1 for existing records (they were already approved)
UPDATE company_recruiters 
SET is_approved = 1 
WHERE is_approved IS NULL;

-- Set joined_at = assigned_at for existing records
UPDATE company_recruiters 
SET joined_at = assigned_at 
WHERE joined_at IS NULL AND assigned_at IS NOT NULL;

-- Set status = 'approved' for existing records
UPDATE company_recruiters 
SET status = 'approved' 
WHERE status IS NULL OR status = '';

-- Set last_activity = assigned_at for existing records
UPDATE company_recruiters 
SET last_activity = COALESCE(assigned_at, GETUTCDATE()) 
WHERE last_activity IS NULL;

GO

-- Create useful indexes for performance
PRINT '4. Creating indexes for better performance...';

-- Index for finding pending requests
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_company_recruiters_status_company')
BEGIN
    CREATE INDEX IX_company_recruiters_status_company 
    ON company_recruiters (status, company_id);
    PRINT '   ✅ Created index IX_company_recruiters_status_company';
END

-- Index for finding user's companies
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_company_recruiters_user_approved')
BEGIN
    CREATE INDEX IX_company_recruiters_user_approved 
    ON company_recruiters (user_id, is_approved);
    PRINT '   ✅ Created index IX_company_recruiters_user_approved';
END

-- Index for activity tracking
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_company_recruiters_last_activity')
BEGIN
    CREATE INDEX IX_company_recruiters_last_activity 
    ON company_recruiters (last_activity DESC);
    PRINT '   ✅ Created index IX_company_recruiters_last_activity';
END

GO

-- Create a view for easy querying of recruiter statistics
PRINT '5. Creating useful views...';

IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_company_recruiter_stats')
BEGIN
    DROP VIEW vw_company_recruiter_stats;
END

CREATE VIEW vw_company_recruiter_stats AS
SELECT 
    cr.company_id,
    c.name as company_name,
    cr.user_id,
    COALESCE(up.first_name + ' ' + up.last_name, u.email) as user_name,
    u.email as user_email,
    cr.role_in_company,
    cr.is_admin,
    cr.is_approved,
    cr.status,
    cr.joined_at,
    cr.last_activity,
    COUNT(p.position_id) as position_count,
    COUNT(CASE WHEN p.is_active = 1 THEN 1 END) as active_position_count,
    COUNT(a.application_id) as total_applications
FROM company_recruiters cr
INNER JOIN companies c ON cr.company_id = c.company_id
INNER JOIN users u ON cr.user_id = u.user_id
LEFT JOIN user_profiles up ON u.user_id = up.user_id
LEFT JOIN positions p ON p.company_id = cr.company_id AND p.created_by = cr.user_id
LEFT JOIN applications a ON a.position_id = p.position_id
GROUP BY 
    cr.company_id, c.name, cr.user_id, up.first_name, up.last_name, u.email,
    cr.role_in_company, cr.is_admin, cr.is_approved, cr.status, cr.joined_at, cr.last_activity;

GO

PRINT '✅ Enhanced company_recruiters schema update completed successfully!';
PRINT '';
PRINT '📊 Summary of changes:';
PRINT '   - Enhanced company_recruiters table with invitation/approval system';
PRINT '   - Added columns: is_approved, joined_at, request_message, response_message';
PRINT '   - Added columns: invited_by, responded_by, responded_at, status, last_activity';
PRINT '   - Created performance indexes';
PRINT '   - Created vw_company_recruiter_stats view for easy querying';
PRINT '';
PRINT '🎯 The system now supports:';
PRINT '   ✅ Recruiter invitation system';
PRINT '   ✅ Company join requests';
PRINT '   ✅ Approval/rejection workflow';
PRINT '   ✅ Advanced statistics and reporting';
PRINT '   ✅ Performance tracking';

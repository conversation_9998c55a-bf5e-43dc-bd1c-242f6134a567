@model DKyThucTap.Models.DTOs.UpdateProfileDto
@{
    ViewData["Title"] = "Chỉnh sửa thông tin";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>Chỉnh sửa thông tin cá nhân
                </h4>
            </div>
            <div class="card-body">
                <form asp-action="Profile" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="FirstName" class="form-label">
                                <i class="fas fa-user me-1"></i>Tên <span class="text-danger">*</span>
                            </label>
                            <input asp-for="FirstName" class="form-control" placeholder="Nhập tên của bạn" />
                            <span asp-validation-for="FirstName" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="LastName" class="form-label">
                                <i class="fas fa-user me-1"></i>Họ <span class="text-danger">*</span>
                            </label>
                            <input asp-for="LastName" class="form-control" placeholder="Nhập họ của bạn" />
                            <span asp-validation-for="LastName" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Phone" class="form-label">
                            <i class="fas fa-phone me-1"></i>Số điện thoại
                        </label>
                        <input asp-for="Phone" class="form-control" placeholder="Nhập số điện thoại" />
                        <span asp-validation-for="Phone" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Address" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>Địa chỉ
                        </label>
                        <textarea asp-for="Address" class="form-control" rows="3" placeholder="Nhập địa chỉ của bạn"></textarea>
                        <span asp-validation-for="Address" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Bio" class="form-label">
                            <i class="fas fa-info-circle me-1"></i>Giới thiệu bản thân
                        </label>
                        <textarea asp-for="Bio" class="form-control" rows="4" placeholder="Viết một vài dòng giới thiệu về bản thân..."></textarea>
                        <span asp-validation-for="Bio" class="text-danger"></span>
                        <div class="form-text">
                            <small>Hãy viết một vài dòng giới thiệu về kinh nghiệm, kỹ năng và mục tiêu nghề nghiệp của bạn.</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="ProfilePictureUrl" class="form-label">
                                <i class="fas fa-image me-1"></i>URL ảnh đại diện
                            </label>
                            <input asp-for="ProfilePictureUrl" class="form-control" placeholder="https://example.com/avatar.jpg" />
                            <span asp-validation-for="ProfilePictureUrl" class="text-danger"></span>
                            <div class="form-text">
                                <small>Nhập URL của ảnh đại diện (tùy chọn)</small>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="CvUrl" class="form-label">
                                <i class="fas fa-file-pdf me-1"></i>URL CV
                            </label>
                            <input asp-for="CvUrl" class="form-control" placeholder="https://example.com/cv.pdf" />
                            <span asp-validation-for="CvUrl" class="text-danger"></span>
                            <div class="form-text">
                                <small>Nhập URL của file CV (tùy chọn)</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Dashboard" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Lưu thay đổi
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        // Preview profile picture
        document.getElementById('ProfilePictureUrl').addEventListener('input', function() {
            const url = this.value;
            const preview = document.getElementById('profile-preview');
            
            if (url && isValidUrl(url)) {
                if (!preview) {
                    const previewDiv = document.createElement('div');
                    previewDiv.id = 'profile-preview';
                    previewDiv.className = 'mt-2 text-center';
                    previewDiv.innerHTML = '<img src="' + url + '" alt="Preview" class="rounded-circle" width="80" height="80" style="object-fit: cover;">';
                    this.parentNode.appendChild(previewDiv);
                } else {
                    preview.innerHTML = '<img src="' + url + '" alt="Preview" class="rounded-circle" width="80" height="80" style="object-fit: cover;">';
                }
            } else if (preview) {
                preview.remove();
            }
        });

        function isValidUrl(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }
    </script>
}

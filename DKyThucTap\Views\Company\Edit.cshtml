@model DKyThucTap.Models.DTOs.Company.UpdateCompanyDto
@{
    ViewData["Title"] = "Chỉnh sửa công ty";
    var companyId = ViewBag.CompanyId as int? ?? 0;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-edit me-2"></i>
                        Chỉnh sửa công ty
                    </h3>
                    <div class="btn-group">
                        <a href="@Url.Action("Details", "Company", new { id = companyId })" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Quay lại
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <form asp-action="Edit" asp-route-id="@companyId" method="post" class="needs-validation" novalidate>
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="row mb-4">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Thông tin cơ bản</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Name" class="form-label">Tên công ty <span class="text-danger">*</span></label>
                                            <input asp-for="Name" class="form-control" required />
                                            <span asp-validation-for="Name" class="text-danger"></span>
                                            <div class="form-text">Tên đầy đủ của công ty</div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="Description" class="form-label">Mô tả</label>
                                            <textarea asp-for="Description" class="form-control" rows="5"></textarea>
                                            <span asp-validation-for="Description" class="text-danger"></span>
                                            <div class="form-text">Mô tả ngắn gọn về công ty, lĩnh vực hoạt động, sứ mệnh, tầm nhìn...</div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="Industry" class="form-label">Ngành nghề</label>
                                            <input asp-for="Industry" class="form-control" />
                                            <span asp-validation-for="Industry" class="text-danger"></span>
                                            <div class="form-text">Lĩnh vực hoạt động chính của công ty</div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="Location" class="form-label">Địa điểm</label>
                                            <input asp-for="Location" class="form-control" />
                                            <span asp-validation-for="Location" class="text-danger"></span>
                                            <div class="form-text">Địa chỉ trụ sở chính của công ty</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Thông tin bổ sung</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="LogoUrl" class="form-label">URL Logo</label>
                                            <input asp-for="LogoUrl" class="form-control" />
                                            <span asp-validation-for="LogoUrl" class="text-danger"></span>
                                            <div class="form-text">Đường dẫn đến logo của công ty</div>
                                            
                                            <div class="mt-2" id="logoPreviewContainer" style="display: none;">
                                                <img id="logoPreview" src="" alt="Logo Preview" 
                                                     class="img-thumbnail" style="max-width: 100%; max-height: 150px;" />
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="Website" class="form-label">Website</label>
                                            <input asp-for="Website" class="form-control" />
                                            <span asp-validation-for="Website" class="text-danger"></span>
                                            <div class="form-text">Website chính thức của công ty</div>
                                        </div>

                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>Lưu ý:</strong> Những thay đổi này sẽ ảnh hưởng đến tất cả vị trí tuyển dụng của công ty.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="@Url.Action("Details", "Company", new { id = companyId })" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times me-1"></i>
                                Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Lưu thay đổi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Logo preview
        document.addEventListener('DOMContentLoaded', function() {
            const logoUrlInput = document.getElementById('LogoUrl');
            const logoPreview = document.getElementById('logoPreview');
            const logoPreviewContainer = document.getElementById('logoPreviewContainer');

            function updateLogoPreview() {
                const url = logoUrlInput.value.trim();
                if (url) {
                    logoPreview.src = url;
                    logoPreviewContainer.style.display = 'block';
                    
                    // Handle image load error
                    logoPreview.onerror = function() {
                        logoPreviewContainer.style.display = 'none';
                    };
                } else {
                    logoPreviewContainer.style.display = 'none';
                }
            }

            logoUrlInput.addEventListener('input', updateLogoPreview);
            updateLogoPreview(); // Initial check
        });

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
}
